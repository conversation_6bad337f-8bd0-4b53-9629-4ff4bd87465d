//+------------------------------------------------------------------+
//|                                      AMPD SELL Separate 2 .mq5   |
//|                                    Copyright 2023,  Ltd.AMPD1951 |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright   "Arise Moroka Prince Dynasty"
#property link        "https://ampd.com"
#property description "WARNING!!! Use @ your Own Risk"
#property version     "V 12.0 ampd separate 2"

#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2
//--- plot Arrows
#property indicator_label1  "BuyArrow"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrGold
#property indicator_width1  1
#property indicator_label2  "SellArrow"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrLime
#property indicator_width2  1
//--- 
enum indicators
  {
   INDICATOR_STOCHASTIC,   //Stochastic Oscillator
   INDICATOR_RSI,          //Relative Strength Index\
  };
//---
input indicators Indicator1=INDICATOR_STOCHASTIC;
input ENUM_TIMEFRAMES TimeFrame1=PERIOD_M10;
//---Range
input string Range;
input int RangePeriod=5;
//---Stoch
input string Stochastic;
input int Kperiod=2;
input int Dperiod=2;
input int Slowing=2;
input ENUM_MA_METHOD StochMAMethod=MODE_EMA;
input ENUM_STO_PRICE PriceField=STO_CLOSECLOSE;
//---RSI
input string RSI;
input int RSIPeriod=2;
input int RSISignal=2;
input ENUM_APPLIED_PRICE RSIPrice=PRICE_CLOSE;

int indicator1;
//---
double Buy[];
double Sell[];
//---
long chartID = ChartID();
#define LabelBox "AMPD"
#define Label1 "SPORT MODE"
#define Label2 "V12"
#define Label3 "PRINCE"
#define Label4 "DYNASTY"
string label1 = "ARISE MOROKA : ",
       label2 = "PRINCE DYNASTY: ";
       
int doubleToPip;
double pipToDouble;
//---
int rangeHandle,
    indHandle1;
//---
MqlTick tick;
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   IndicatorSetString(INDICATOR_SHORTNAME, "Arise Moroka Prince Dynasty");

   // Create a black-filled box with the text
   ObjectCreate(chartID, LabelBox, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_XDISTANCE, 6);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_YDISTANCE, 9);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_XSIZE, 120);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_YSIZE, 40);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BGCOLOR, clrNONE);

   // Set the box's border color to Steel Blue
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BORDER_TYPE, 0);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BORDER_COLOR,clrSteelBlue);

   // Create the text label
   ObjectCreate(chartID, Label1, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label1, OBJPROP_XDISTANCE, 7);
   ObjectSetInteger(chartID, Label1, OBJPROP_YDISTANCE, 80);
   ObjectSetString(chartID, Label1, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label1, OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(chartID, Label1, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(chartID, Label1, OBJPROP_CORNER, 0);
   ObjectSetString(chartID, Label1, OBJPROP_TEXT, "Arise Moroka Prince Dynasty");
   ObjectSetString(chartID, Label1, OBJPROP_FONT, "Bolt Italic");
   ObjectSetInteger(chartID, Label1, OBJPROP_FONTSIZE, 16);
   IndicatorSetString(INDICATOR_SHORTNAME,"Indicator Arrows");
//--- set points & digits
   IndicatorSetInteger(INDICATOR_DIGITS,_Digits);   
   if(_Digits==2 || _Digits==3)  
      doubleToPip = 100;
   else                          
      doubleToPip = 10000;
   
   if(_Digits==2 || _Digits==4) 
      pipToDouble = _Point;
   else                       
      pipToDouble = _Point*10;
//---create label rectangle and labels
   string label3,
          label4;
   int xStart=7;
   int yStart=80;
   int yIncrement=14;
   int ySize=40;
   int ySizeInc=15;
   ObjectCreate(chartID,LabelBox,OBJ_RECTANGLE_LABEL,0,0,0);
   ObjectSetInteger(chartID,LabelBox,OBJPROP_XDISTANCE,2);
   ObjectSetInteger(chartID,LabelBox,OBJPROP_YDISTANCE,4);
   ObjectSetInteger(chartID,LabelBox,OBJPROP_XSIZE,120);
   ObjectSetInteger(chartID,LabelBox,OBJPROP_YSIZE,ySize);
   ObjectSetInteger(chartID,LabelBox,OBJPROP_BGCOLOR,clrSteelBlue);
   ObjectSetInteger(chartID,LabelBox,OBJPROP_BORDER_TYPE,OBJPROP_BORDER_COLOR);

   ObjectCreate(chartID,Label1,OBJ_LABEL,0,0,0);
   ObjectSetInteger(chartID,Label1,OBJPROP_XDISTANCE,xStart);
   ObjectSetInteger(chartID,Label1,OBJPROP_YDISTANCE,yStart);
   ObjectSetString(chartID,Label1,OBJPROP_FONT,"Arial Bold");
   ObjectSetInteger(chartID,Label1,OBJPROP_FONTSIZE,10);
   ObjectSetInteger(chartID,Label1,OBJPROP_COLOR,clrBlueViolet);
   
   ObjectCreate(chartID,Label2,OBJ_LABEL,0,0,0);
   ObjectSetInteger(chartID,Label2,OBJPROP_XDISTANCE,xStart);
   ObjectSetInteger(chartID,Label2,OBJPROP_YDISTANCE,yStart+=yIncrement);
   ObjectSetString(chartID,Label2,OBJPROP_FONT,"Arial Bold");
   ObjectSetInteger(chartID,Label2,OBJPROP_FONTSIZE,10);
   ObjectSetInteger(chartID,Label2,OBJPROP_COLOR,clrBlueViolet);
//---
   string timeFrame1 = StringSubstr(EnumToString(TimeFrame1),7)+" ";
   if(timeFrame1=="CURRENT ")
      timeFrame1 = "";
//---
   rangeHandle = iATR(NULL,0,RangePeriod);
   if(rangeHandle==INVALID_HANDLE)
      return(INIT_FAILED);
   indHandle1 = INVALID_HANDLE;
   if(Indicator1==INDICATOR_RSI)
      Alert("Indicator1 can't be 'No Indicator'");
   switch(Indicator1)
     {
      case INDICATOR_STOCHASTIC:
         label3 = StringFormat("iStoch %s (%d,%d,%d) %s",timeFrame1,
                  Kperiod,Dperiod,Slowing,
                  StringSubstr(EnumToString(StochMAMethod),5));
         indHandle1 = iStochastic(_Symbol,TimeFrame1,Kperiod,Dperiod,Slowing,StochMAMethod,PriceField);
         break;
      case INDICATOR_RSI:
         label3 = StringFormat("iRSI %s (%d,%d)",timeFrame1,
                  RSIPeriod,RSISignal);
         indHandle1 = iRSI(_Symbol,TimeFrame1,RSIPeriod,RSIPrice);
         break;
     }
   if(indHandle1==INVALID_HANDLE)
      return(INIT_FAILED);
   ObjectSetInteger(chartID,LabelBox,OBJPROP_YSIZE,ySize+=ySizeInc);
   ObjectCreate(chartID,Label3, OBJ_LABEL,0,0,0);
   ObjectSetInteger(chartID,Label3,OBJPROP_CORNER,0);
   ObjectSetInteger(chartID,Label3,OBJPROP_XDISTANCE,xStart);
   ObjectSetInteger(chartID,Label3,OBJPROP_YDISTANCE,yStart+=yIncrement);
   ObjectSetString(chartID,Label3,OBJPROP_TEXT,"Drift 360 765Mc");
   ObjectSetString(chartID,Label3,OBJPROP_FONT,"Arial Bold");
   ObjectSetInteger(chartID,Label3,OBJPROP_FONTSIZE,10);
   ObjectSetInteger(chartID,Label3,OBJPROP_COLOR,clrBlueViolet);
//---
   ObjectSetInteger(chartID,LabelBox,OBJPROP_YSIZE,ySize+=ySizeInc);
   ObjectCreate(chartID,Label4,OBJ_LABEL,0,0,0);
   ObjectSetInteger(chartID,Label4,OBJPROP_CORNER, 0);
   ObjectSetInteger(chartID,Label4,OBJPROP_XDISTANCE,xStart);
   ObjectSetInteger(chartID,Label4,OBJPROP_YDISTANCE,yStart+=yIncrement);
   ObjectSetString(chartID,Label4,OBJPROP_TEXT,"V12.0");
   ObjectSetString(chartID,Label4,OBJPROP_FONT,"Arial Bold");
   ObjectSetInteger(chartID,Label4,OBJPROP_FONTSIZE,10);
   ObjectSetInteger(chartID,Label4,OBJPROP_COLOR,clrBlueViolet);
//--- indicator buffers mapping
   SetIndexBuffer(0,Buy,INDICATOR_DATA);
   ArraySetAsSeries(Buy,true);
   PlotIndexSetString(0,PLOT_LABEL,"BuyArrow");   
   PlotIndexSetInteger(0,PLOT_ARROW,233);
   PlotIndexSetDouble(0,PLOT_EMPTY_VALUE,0);
   SetIndexBuffer(1,Sell,INDICATOR_DATA);
   ArraySetAsSeries(Sell,true);
   PlotIndexSetString(1,PLOT_LABEL,"SellArrow");   
   PlotIndexSetInteger(1,PLOT_ARROW,234);
   PlotIndexSetDouble(1,PLOT_EMPTY_VALUE,0);
//---
   return(INIT_SUCCEEDED);
  }
//---
enum ENUM_SIGNAL
  {
   SIGNAL_NONE,
   SIGNAL_BUY,
   SIGNAL_SELL
  };
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   if(rates_total<100)  
      return(0);
//---
   ArraySetAsSeries(time,true);
   ArraySetAsSeries(high,true);
   ArraySetAsSeries(low,true);
//---
   if(SymbolInfoTick(_Symbol,tick))
      ObjectSetString(chartID,Label1,OBJPROP_TEXT,label1 + 
                      DoubleToString((tick.ask-tick.bid)*doubleToPip,1));
   static datetime prevTime;
   if(prev_calculated>0 && time[0]!=prevTime)
     {
      prevTime = time[0];
      ObjectSetString(chartID,Label2,OBJPROP_TEXT,label2 + 
                      StringFormat("(%d) %4.1f",RangePeriod,Range(0)*doubleToPip));
     }
   int i,limit;
//---
   limit = rates_total - prev_calculated;
   if(prev_calculated==0)
     { 
      limit = (int)ChartGetInteger(chartID,CHART_VISIBLE_BARS)+100;
      PlotIndexSetInteger(0,PLOT_DRAW_BEGIN,rates_total-limit);
      PlotIndexSetInteger(1,PLOT_DRAW_BEGIN,rates_total-limit);
     }
//---
   for(i=limit;i>=0 && !IsStopped();i--)
     {
      static datetime Time[1];
      if(CopyTime(_Symbol,TimeFrame1,time[i],1,Time)!=1)
         return(0);
      const datetime time1 = Time[0];
      switch(Indicator1)
        {
        
         case INDICATOR_STOCHASTIC:
           { 
            static double Stoc[2];
            static double Sign[2];
            if(CopyBuffer(indHandle1,0,time1,2,Stoc)!=2)
               return(0);
            if(CopyBuffer(indHandle1,1,time1,2,Sign)!=2)
               return(0);
            indicator1 = iStochastic(Stoc,Sign);
            break;
           }
         case INDICATOR_RSI:
           { 
            static double Rsi[];
            if(ArraySize(Rsi)!=RSISignal+1)
               ArrayResize(Rsi,RSISignal+1);
            if(CopyBuffer(indHandle1,0,time1,RSISignal+1,Rsi)!=RSISignal+1)
               return(0);
            indicator1 = iRSI(Rsi);
            break;
           }

        }

      if(indicator1==SIGNAL_BUY)
        {
         Buy[i] = low[i] - Range(i); 
         AlertsHandle(time[0],SIGNAL_BUY);
         Sell[i] = 0;
        }
      else if(indicator1==SIGNAL_SELL)   
        {
         Sell[i] = high[i] + Range(i); 
         AlertsHandle(time[0],SIGNAL_SELL);
         Buy[i] = 0;
        }
      else
        {
         Buy[i] = 0;
         Sell[i]= 0;
        } 
     }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
//---check & delete labels
   if(ObjectFind(0,LabelBox)!=-1)
      ObjectDelete(0,LabelBox);
   if(ObjectFind(0,Label1)!=-1)
      ObjectDelete(0,Label1);
   if(ObjectFind(0,Label2)!=-1)
      ObjectDelete(0,Label2);
   if(ObjectFind(0,Label3)!=-1)
      ObjectDelete(0,Label3);
   if(ObjectFind(0,Label4)!=-1)
      ObjectDelete(0,Label4);
   IndicatorRelease(rangeHandle);
   IndicatorRelease(indHandle1);
   return;
  }
//+------------------------------------------------------------------+
//|  Average Range                                                   |
//+------------------------------------------------------------------+
double Range(int idx)
  {
   static double range[1];
   if(CopyBuffer(rangeHandle,0,idx+1,1,range)!=1)
      return(0.0);
   double avgRange = range[0];
   return(avgRange);
  }
//+------------------------------------------------------------------+
//|  Moving Average                                                  |
//+------------------------------------------------------------------+
int iMA(const double &moav[],
        const double &high[],
        const double &low[],
        const double &close[])
  { 
   int signal=SIGNAL_NONE;
   if(close[0]>moav[0] && low[0]<moav[0])
      signal=SIGNAL_BUY;
   if(close[0]<moav[0] && high[0]>moav[0])
      signal=SIGNAL_SELL;
//---
   return(signal);
  }
//+------------------------------------------------------------------+
//|  Stochastic Oscillator                                           |
//+------------------------------------------------------------------+
int iStochastic(const double &stoc[],
                const double &sign[])
  { 
   double currStoc = stoc[1];
   double prevStoc = stoc[0];
   double currSign = sign[1];
   double prevSign = sign[0];
//---
   int signal=SIGNAL_NONE;
   if(currStoc>currSign && prevStoc<prevSign)
      signal=SIGNAL_BUY;
   if(currStoc<currSign && prevStoc>prevSign)
      signal=SIGNAL_SELL;
//---
   return(signal);
  }            
//+------------------------------------------------------------------+
//|  Relative Strength Index                                         |
//+------------------------------------------------------------------+
int iRSI(const double &rsi[])
  { 
   ArraySetAsSeries(rsi,true);
   double sum1 = 0.0;
   double sum2 = 0.0;
   for(int i=0,j=1;i<RSISignal;i++,j++)
     { 
      sum1 += rsi[i];
      sum2 += rsi[j];
     }
   double currRsi = rsi[0];
   double prevRsi = rsi[1];
   double currSig = sum1 / RSISignal;
   double prevSig = sum2 / RSISignal;
//---
   int signal=SIGNAL_NONE;
   if(currRsi>currSig && prevRsi<prevSig)
      signal=SIGNAL_BUY;
   if(currRsi<currSig && prevRsi>prevSig)
      signal=SIGNAL_SELL;
//---
   return(signal);
  }                                 
         
//+------------------------------------------------------------------+
//|  Alerts Handle                                                   |
//+------------------------------------------------------------------+
void AlertsHandle(const datetime &time,
                  const ENUM_SIGNAL alert_type)
  {
   static datetime timePrev;
   static ENUM_SIGNAL typePrev;
   string alertMessage;
   double price = 0.0;
   if(SymbolInfoTick(_Symbol,tick))
      price = tick.bid;
   
   if(timePrev!=time || typePrev!=alert_type)
     {
      timePrev = time;
      typePrev = alert_type;
      alertMessage = StringFormat("%s @ %s %s @ %s",_Symbol,TimeToString(TimeLocal(),TIME_MINUTES),
                                  StringSubstr(EnumToString(alert_type),7),DoubleToString(price,_Digits));
     }
  }
//+------------------------------------------------------------------+
